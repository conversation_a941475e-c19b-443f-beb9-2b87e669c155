#!/bin/bash

# 测试启动时序竞态条件修复效果的脚本
# 该脚本会多次运行集成测试，统计成功率

echo "=========================================="
echo "    启动时序竞态条件修复测试脚本"
echo "=========================================="

# 配置参数
TOTAL_TESTS=10
SUCCESS_COUNT=0
FAILED_COUNT=0
TEST_TIMEOUT=300  # 5分钟超时

# 创建日志目录
LOG_DIR="race_condition_test_logs"
mkdir -p "$LOG_DIR"

echo "将进行 $TOTAL_TESTS 次测试，每次测试超时时间为 $TEST_TIMEOUT 秒"
echo "测试日志将保存在 $LOG_DIR 目录中"
echo ""

# 确保在测试目录中
cd /home/<USER>/Raft-KV/tests

for i in $(seq 1 $TOTAL_TESTS); do
    echo "========== 测试 $i/$TOTAL_TESTS =========="
    
    # 清理之前的测试残留
    pkill -f integration_test 2>/dev/null
    pkill -f KvServer 2>/dev/null
    sleep 2
    
    # 删除可能存在的配置文件
    rm -f integration_test.conf test.conf
    
    LOG_FILE="$LOG_DIR/test_${i}.log"
    
    echo "开始测试 $i，日志文件: $LOG_FILE"
    
    # 运行测试，设置超时
    timeout $TEST_TIMEOUT ../bin/integration_test > "$LOG_FILE" 2>&1
    TEST_RESULT=$?
    
    if [ $TEST_RESULT -eq 0 ]; then
        echo "✅ 测试 $i 成功"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    elif [ $TEST_RESULT -eq 124 ]; then
        echo "❌ 测试 $i 超时失败"
        FAILED_COUNT=$((FAILED_COUNT + 1))
        echo "TIMEOUT" >> "$LOG_FILE"
    else
        echo "❌ 测试 $i 失败 (退出码: $TEST_RESULT)"
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
    
    # 强制清理所有相关进程
    pkill -f integration_test 2>/dev/null
    pkill -f KvServer 2>/dev/null
    sleep 1
    
    echo ""
done

echo "=========================================="
echo "           测试结果统计"
echo "=========================================="
echo "总测试次数: $TOTAL_TESTS"
echo "成功次数: $SUCCESS_COUNT"
echo "失败次数: $FAILED_COUNT"
echo "成功率: $(echo "scale=2; $SUCCESS_COUNT * 100 / $TOTAL_TESTS" | bc)%"
echo ""

if [ $SUCCESS_COUNT -gt $((TOTAL_TESTS * 7 / 10)) ]; then
    echo "🎉 测试结果良好！成功率超过70%，启动时序竞态条件得到显著改善。"
elif [ $SUCCESS_COUNT -gt $((TOTAL_TESTS / 2)) ]; then
    echo "⚠️  测试结果一般。成功率超过50%，但仍有改进空间。"
else
    echo "❌ 测试结果不理想。成功率低于50%，需要进一步优化。"
fi

echo ""
echo "详细日志文件位于: $LOG_DIR/"
echo "可以查看失败的测试日志来分析问题原因。"

# 分析失败原因
echo ""
echo "========== 失败原因分析 =========="
if [ $FAILED_COUNT -gt 0 ]; then
    echo "分析失败的测试日志..."
    
    TIMEOUT_COUNT=$(grep -l "TIMEOUT" $LOG_DIR/test_*.log 2>/dev/null | wc -l)
    CONNECTION_FAIL_COUNT=$(grep -l "connect fail" $LOG_DIR/test_*.log 2>/dev/null | wc -l)
    LEADER_NOT_FOUND_COUNT=$(grep -l "未找到领导者" $LOG_DIR/test_*.log 2>/dev/null | wc -l)
    
    echo "超时失败: $TIMEOUT_COUNT 次"
    echo "连接失败: $CONNECTION_FAIL_COUNT 次"
    echo "未找到领导者: $LEADER_NOT_FOUND_COUNT 次"
else
    echo "所有测试都成功了！"
fi

echo ""
echo "测试完成。"
